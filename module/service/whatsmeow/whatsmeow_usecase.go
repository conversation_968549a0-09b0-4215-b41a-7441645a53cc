package whatsmeow

import (
	"context"
	"fmt"

	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	"go.mau.fi/whatsmeow/store"
	"go.mau.fi/whatsmeow/store/sqlstore"
)

type whatsMeowService struct {
	Session   WhatsAppSession
	container *sqlstore.Container
}

func NewWhatsMeowService(isOfficial bool) domain.WhatsAppService {
	container, err := getContainer()
	if err != nil {
		log.Info("init container err: %v", err)
	}
	service := whatsMeowService{container: container}

	if isOfficial {
		service.Session = service.initSession()
	}

	return service
}

// utility
func (w whatsMeowService) SetDeviceName(name string) {
	store.DeviceProps.Os = &name
}

func (w whatsMeowService) Login(id string, qrChan chan string, status chan domain.LoginStatus) error {
	return initDynamicSession(w.container, id, qrChan, status)
}

func (s whatsMeowService) Logout(id string) error {
	ses, err := getSession(s.container, id)
	if log.IfError(err) {
		return err
	}

	err = ses.client.Logout(context.Background())
	log.IfError(err)
	return err
}

func (w whatsMeowService) GetSession(id string) (domain.WhatsAppService, error) {
	ses, err := getSession(w.container, id)
	if err != nil {
		fmt.Println("failed to get dynamic session: ", err)
		return nil, err
	}
	return whatsMeowService{Session: ses}, nil
}

//func (w whatsMeowService) GetSession(id string) (WhatsAppSession, error) {
//	return getDynamicSession(id)
//}

func (w whatsMeowService) SendWhatsAppMessage(whatsApp domain.WhatsApp) (models.MessageResponse, error) {
	return w.Session.SendMessage(whatsApp)
}
