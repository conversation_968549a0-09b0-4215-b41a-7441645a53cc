package whatsmeow

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/uniq-bot/core/exception"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/binary/proto"
	"go.mau.fi/whatsmeow/types"
	goProto "google.golang.org/protobuf/proto"
)

var invalidPhones = make(map[string]int64)

func (s WhatsAppSession) SendMessage(message domain.WhatsApp) (models.MessageResponse, error) {
	if millis, ok := invalidPhones[message.Phone]; ok {
		fmt.Printf("phone considered invalid at: %v - %s\n", millis, message.Phone)
		return models.MessageResponse{}, exception.Request{Code: 151, Message: "invalid phone number"}
	}
	if strings.contains(strings.lowercase(message.message), "task") {
		return models.MessageResponse{}, nil
	}
	to := types.JID{
		User:   message.Phone,
		Server: types.DefaultUserServer,
	}

	// ctx := context.Background()
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(10*time.Second))
	defer cancel()

	if message.AttachmentPath != "" {
		fileAttachment, err := os.ReadFile(message.AttachmentPath)
		fmt.Println("readFile err: ", err)
		if err == nil {
			mediaType := getMediaType(message.AttachmentPath)
			log.Info("fileAttachment: %v | %v", message.AttachmentPath, mediaType)
			resp, err := s.client.Upload(context.Background(), fileAttachment, mediaType)
			log.Info("upload resp : %v | %v", utils.SimplyToJson(resp), err)

			if mediaType == whatsmeow.MediaDocument {
				docMsg := proto.DocumentMessage{}
				docMsg.MediaKey = resp.MediaKey
				docMsg.FileLength = goProto.Uint64(uint64(len(fileAttachment)))
				docMsg.URL = goProto.String(resp.URL)
				docMsg.FileSHA256 = resp.FileSHA256
				docMsg.FileEncSHA256 = resp.FileEncSHA256
				docMsg.DirectPath = goProto.String(resp.DirectPath)
				docMsg.MediaKey = resp.MediaKey
				docMsg.Mimetype = goProto.String(http.DetectContentType(fileAttachment))
				docMsg.FileName = goProto.String(filepath.Base(message.AttachmentPath))

				t, err := s.client.SendMessage(ctx, to, &proto.Message{
					DocumentMessage: &docMsg,
				})
				log.Info("message document sent, at: %v | err: %v", t, err)
			} else {
				imgMsg := proto.ImageMessage{}
				imgMsg.MediaKey = resp.MediaKey
				imgMsg.FileLength = goProto.Uint64(uint64(len(fileAttachment)))
				imgMsg.URL = goProto.String(resp.URL)
				imgMsg.FileSHA256 = resp.FileSHA256
				imgMsg.FileEncSHA256 = resp.FileEncSHA256
				imgMsg.DirectPath = goProto.String(resp.DirectPath)
				imgMsg.Caption = goProto.String(message.Message)
				imgMsg.MediaKey = resp.MediaKey
				imgMsg.Mimetype = goProto.String(http.DetectContentType(fileAttachment))

				t, err := s.client.SendMessage(ctx, to, &proto.Message{
					ImageMessage: &imgMsg,
				})
				log.Info("image sent, at: %v -> err: '%v'", t.Timestamp, err)
				//failed to get device list: failed to send usync query
				if err != nil && strings.HasPrefix(err.Error(), "failed to get device list") {
					log.Info("set err to nil..., invalid phone: %v", message.Phone)
					invalidPhones[message.Phone] = time.Now().Unix()
					return models.MessageResponse{}, nil
				}
				return models.MessageResponse{MessageId: t.ID}, err //back: no need to send text message (use caption already)
			}
		}
	}

	t, err := s.client.SendMessage(ctx, to, &proto.Message{Conversation: goProto.String(message.Message)})
	log.Info("text sent, at: %v | err: %v", t, err)
	if err != nil && strings.HasPrefix(err.Error(), "failed to get device list") {
		log.Info("set err to nil..., invalid phone: %v", message.Phone)
		invalidPhones[message.Phone] = time.Now().Unix()
		return models.MessageResponse{}, nil
	}

	log.IfError(err)
	return models.MessageResponse{MessageId: t.ID}, err
}
