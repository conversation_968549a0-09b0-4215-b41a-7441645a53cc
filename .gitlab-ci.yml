variables:
  GIT_SUBMODULE_STRATEGY: recursive
  APP_NAME: uniq-bot-dynamic
  IMAGE_TAG: ${CI_REGISTRY}/${CI_PROJECT_PATH}:${CI_COMMIT_REF_NAME}-latest

stages:
  - build
  - deploy

build_image:
  stage: build
  image: docker:26.1.1-alpine3.19
  services:
    - docker:26.1.1-dind-alpine3.19
  rules:
    - if: $SKIP_BUILD == "true"
      when: never
    - if: $CI_COMMIT_REF_NAME == "master"
      when: on_success
    - if: $CI_COMMIT_REF_NAME == "staging"
      when: on_success
    - if: $CI_COMMIT_REF_NAME == "dev"
      when: on_success
  script:
    - echo ${FIREBASE_SDK_CREDENTIAL} | base64 -d > config/auth/chat-support-102fc-firebase-adminsdk.json
    - echo ${G_PUBSUB_RW_KEY} | base64 -d > config/auth/pubsub_credential.json
    - cat $GCLOUD_SERVICE_ACCOUNT >  config/auth/gcloud_service_account.json
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - docker build -t ${IMAGE_TAG} --pull .
    - docker push ${IMAGE_TAG}
  after_script:
    - docker logout ${CI_REGISTRY}
  tags:
    - testing-docker

# Common deployment template
.deploy_template: &deploy_template
  stage: deploy
  image: docker/compose:alpine-1.29.2
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - if [ -n "$ENV_FILE" ]; then echo ${ENV_FILE} | base64 -d > .env; fi
    - docker pull ${IMAGE_TAG}
    - docker container rm -f ${CONTAINER_NAME} || true
    - docker run -d --restart unless-stopped --name ${CONTAINER_NAME} --network uniq-network ${DOCKER_ARGS} ${IMAGE_TAG}

deploy_dev:
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev"
  variables:
    CONTAINER_NAME: uniq-bot-dynamic
    DOCKER_ARGS: "-p 1819:1719 -v /docker/wabot_dynamic:/temp/sessions -e wa_type=dynamic -e server=dev -m=250m --memory-swappiness=0"
  tags:
    - testing-docker

deploy_staging:
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "staging"
  variables:
    CONTAINER_NAME: $APP_NAME
    ENV_FILE: $ENV_STAGING
    DOCKER_ARGS: "-p 1819:1719 -v /docker/wabot_dynamic:/temp/sessions -e wa_type=dynamic -m=500m --log-driver=gcplogs --env-file=.env"
  tags:
    - staging

deployProductionOfficial:
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "master"
  variables:
    CONTAINER_NAME: uniq-bot
    DOCKER_ARGS: "-p 1719:1719 -v /wabot:/temp/sessions -e wa_type=official -e server=production -e GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn -m=350m --log-driver=gcplogs"
  tags:
    - production

deployProductionDynamic:
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_REF_NAME == "master-test"
  variables:
    CONTAINER_NAME: uniq-bot-dynamic
    DOCKER_ARGS: "-p 1819:1719 -v /wabot_dynamic:/temp/sessions -e wa_type=dynamic -e server=production -e GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn -m=350m --memory-swappiness=0 --log-driver=gcplogs"
  tags:
    - production
